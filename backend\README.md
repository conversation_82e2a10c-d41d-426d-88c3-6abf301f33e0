# MedTrack Hub Backend

A comprehensive medical education platform backend built with NestJS, TypeScript, and PostgreSQL.

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-20+-green.svg)](https://nodejs.org/)
[![NestJS](https://img.shields.io/badge/NestJS-11+-red.svg)](https://nestjs.com/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5+-blue.svg)](https://typescriptlang.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15+-blue.svg)](https://postgresql.org/)
[![Redis](https://img.shields.io/badge/Redis-7+-red.svg)](https://redis.io/)

## 🚀 Features

### 🔐 Authentication & Security
- **JWT Authentication** with refresh tokens and secure session management
- **Role-based Access Control** (Student, Instructor, Admin, Attending Physician)
- **Two-Factor Authentication** with TOTP support using Speakeasy
- **Password Security** with Argon2 hashing
- **Security Monitoring** with audit logging and threat detection
- **Rate Limiting** and request throttling

### 📚 Learning Management
- **Course Management** with hierarchical categories and enrollment tracking
- **Learning Paths** with milestone-based progression
- **Learning Goals** with SMART objectives and progress tracking
- **Study Materials** with AWS S3 file upload support
- **Interactive Quizzes** with multiple question types and instant feedback
- **Progress Analytics** with detailed performance insights

### 🏥 Clinical Education
- **Clinical Case Studies** with complexity levels and specialty focus
- **Patient Simulation** scenarios with diagnostic workflows
- **Medical Knowledge Base** with evidence-based content
- **Specialty-specific Learning** paths for different medical fields
- **Assessment Tools** for clinical competency evaluation

### 🤖 AI-Powered Features
- **AI Tutoring** with Claude integration for personalized assistance
- **Smart Recommendations** based on learning patterns and performance
- **Performance Prediction** using advanced analytics algorithms
- **Adaptive Learning Paths** with dynamic content adjustment
- **Automated Study Plans** generated based on individual needs

### 📊 Analytics & Insights
- **Learning Pattern Analysis** with Python-based ML algorithms
- **Performance Metrics** with real-time progress tracking
- **Study Habit Insights** and personalized recommendations
- **Predictive Analytics** for learning outcome optimization
- **Comprehensive Reporting** with exportable dashboards

### 🔔 Communication & Collaboration
- **Real-time Notifications** system with customizable preferences
- **User Feedback** and rating system for continuous improvement
- **Discussion Forums** and chat functionality
- **Collaborative Study Groups** with shared resources

## 🛠️ Tech Stack

- **Framework**: NestJS (Node.js)
- **Language**: TypeScript
- **Database**: PostgreSQL with TypeORM
- **Cache**: Redis with ioredis
- **Authentication**: JWT with Passport
- **Password Hashing**: Argon2
- **2FA**: Speakeasy (TOTP)
- **File Storage**: AWS S3
- **AI**: Anthropic Claude API
- **Testing**: Jest (Unit, Integration, E2E)
- **Documentation**: Swagger/OpenAPI
- **Validation**: class-validator & class-transformer
- **Rate Limiting**: @nestjs/throttler
- **Health Checks**: @nestjs/terminus
- **Containerization**: Docker

## 🚀 Quick Start

### Prerequisites
- Node.js 20+
- PostgreSQL 15+
- Redis 7+
- pnpm (recommended) or npm

### Installation

```bash
# Install dependencies
pnpm install

# Copy environment template
cp .env.example .env
# Edit .env with your configuration
```

### Development

```bash
# Start in development mode
pnpm run start:dev

# Start with debug mode
pnpm run start:debug

# Build for production
pnpm run build

# Start production server
pnpm run start:prod
```

### Testing

```bash
# Run unit tests
pnpm run test

# Run e2e tests
pnpm run test:e2e

# Run integration tests
pnpm run test:integration

# Run all tests
pnpm run test:all

# Generate test coverage
pnpm run test:cov
```

## Deployment

When you're ready to deploy your NestJS application to production, there are some key steps you can take to ensure it runs as efficiently as possible. Check out the [deployment documentation](https://docs.nestjs.com/deployment) for more information.

If you are looking for a cloud-based platform to deploy your NestJS application, check out [Mau](https://mau.nestjs.com), our official platform for deploying NestJS applications on AWS. Mau makes deployment straightforward and fast, requiring just a few simple steps:

```bash
$ pnpm install -g mau
$ mau deploy
```

With Mau, you can deploy your application in just a few clicks, allowing you to focus on building features rather than managing infrastructure.

## Resources

Check out a few resources that may come in handy when working with NestJS:

- Visit the [NestJS Documentation](https://docs.nestjs.com) to learn more about the framework.
- For questions and support, please visit our [Discord channel](https://discord.gg/G7Qnnhy).
- To dive deeper and get more hands-on experience, check out our official video [courses](https://courses.nestjs.com/).
- Deploy your application to AWS with the help of [NestJS Mau](https://mau.nestjs.com) in just a few clicks.
- Visualize your application graph and interact with the NestJS application in real-time using [NestJS Devtools](https://devtools.nestjs.com).
- Need help with your project (part-time to full-time)? Check out our official [enterprise support](https://enterprise.nestjs.com).
- To stay in the loop and get updates, follow us on [X](https://x.com/nestframework) and [LinkedIn](https://linkedin.com/company/nestjs).
- Looking for a job, or have a job to offer? Check out our official [Jobs board](https://jobs.nestjs.com).

## Support

Nest is an MIT-licensed open source project. It can grow thanks to the sponsors and support by the amazing backers. If you'd like to join them, please [read more here](https://docs.nestjs.com/support).

## Stay in touch

- Author - [Kamil Myśliwiec](https://twitter.com/kammysliwiec)
- Website - [https://nestjs.com](https://nestjs.com/)
- Twitter - [@nestframework](https://twitter.com/nestframework)

## License

Nest is [MIT licensed](https://github.com/nestjs/nest/blob/master/LICENSE).
