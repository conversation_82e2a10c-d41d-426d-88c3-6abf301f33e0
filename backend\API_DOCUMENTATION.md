# MedTrack Hub API Documentation

## 📋 Overview

The MedTrack Hub API is a comprehensive RESTful API built with NestJS that provides endpoints for medical education platform functionality. All endpoints are versioned (v1) and require authentication unless otherwise specified.

**Base URL**: `http://localhost:3002/v1`
**API Documentation**: `http://localhost:3002/api/docs` (Swagger UI)

## 🔐 Authentication

### JWT Authentication
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

### Authentication Endpoints

#### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "firstName": "string",
  "lastName": "string",
  "role": "student" | "instructor" | "admin" | "attending_physician"
}
```

#### POST /auth/login
Authenticate user and receive JWT tokens.

**Request Body:**
```json
{
  "email": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "access_token": "string",
  "refresh_token": "string",
  "user": {
    "id": "string",
    "email": "string",
    "username": "string",
    "role": "string"
  }
}
```

#### GET /auth/profile
Get current user profile information.

#### GET /auth/session
Get current session information.

#### POST /auth/refresh
Refresh JWT access token using refresh token.

## 👥 User Management

#### GET /users
Get list of users (Admin only).

#### POST /users
Create a new user (Admin only).

## 📚 Course Management

#### GET /courses
Get list of available courses.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `category`: Filter by category
- `level`: Filter by difficulty level

#### POST /courses
Create a new course (Instructor/Admin only).

#### GET /courses/:id
Get detailed course information.

#### PATCH /courses/:id
Update course information (Instructor/Admin only).

#### DELETE /courses/:id
Delete a course (Admin only).

#### POST /courses/:id/enroll
Enroll in a course.

#### DELETE /courses/:id/enroll
Unenroll from a course.

#### GET /courses/featured
Get featured courses.

#### GET /courses/recommended
Get personalized course recommendations.

#### GET /courses/my-courses
Get user's enrolled courses.

## 📂 Course Categories

#### GET /course-categories
Get list of course categories.

#### POST /course-categories
Create a new category (Admin only).

#### GET /course-categories/:id
Get category details.

#### GET /course-categories/hierarchy
Get hierarchical category structure.

## 📖 Learning Units

#### GET /units
Get list of learning units.

#### POST /units
Create a new unit (Instructor/Admin only).

#### GET /units/:id
Get unit details.

#### PUT /units/:id
Update unit (Instructor/Admin only).

#### DELETE /units/:id
Delete unit (Admin only).

## 📄 Study Materials

#### GET /materials
Get list of study materials.

#### POST /materials
Create new study material (Instructor/Admin only).

#### GET /materials/:id
Get material details.

#### DELETE /materials/:id
Delete material (Admin only).

#### POST /materials/upload
Upload study material files.

#### POST /materials/share
Share materials with other users.

## 🎯 Learning Paths

#### GET /learning-paths
Get list of learning paths.

#### POST /learning-paths
Create a new learning path (Instructor/Admin only).

#### GET /learning-paths/:id
Get learning path details.

#### PATCH /learning-paths/:id
Update learning path.

#### DELETE /learning-paths/:id
Delete learning path (Admin only).

#### POST /learning-paths/:id/enroll
Enroll in a learning path.

#### GET /learning-paths/:id/progress
Get learning path progress.

#### PATCH /learning-paths/:id/progress
Update learning path progress.

#### GET /learning-paths/recommendations
Get personalized learning path recommendations.

#### GET /learning-paths/trending
Get trending learning paths.

## 🎯 Learning Goals

#### GET /learning-goals
Get list of learning goals.

#### POST /learning-goals
Create a new learning goal.

#### GET /learning-goals/:id
Get learning goal details.

#### PATCH /learning-goals/:id
Update learning goal.

#### DELETE /learning-goals/:id
Delete learning goal.

#### POST /learning-goals/:id/progress
Update goal progress.

#### GET /learning-goals/:id/progress
Get goal progress.

#### POST /learning-goals/smart-suggestions
Get SMART goal suggestions.

## 📈 Progress Tracking

#### POST /progress/:unitId
Update progress for a specific unit.

#### GET /progress/user/:userId
Get user's overall progress.

## 🧪 Quizzes & Assessments

#### GET /quiz/unit/:unitId
Get quiz for a specific unit.

#### POST /quiz/submit
Submit quiz answers.

#### GET /quiz/results/:userId/:unitId
Get quiz results.

#### GET /quiz/rapid-review/:userId
Get rapid review questions.

## 🏥 Clinical Cases

#### GET /clinical-cases
Get list of clinical cases.

#### POST /clinical-cases
Create a new clinical case (Instructor/Admin only).

#### GET /clinical-cases/:id
Get clinical case details.

#### POST /clinical-cases/:id/start
Start a clinical case attempt.

#### PATCH /clinical-cases/attempts/:attemptId/progress
Update case attempt progress.

#### POST /clinical-cases/attempts/:attemptId/diagnosis
Submit diagnosis for case.

#### POST /clinical-cases/attempts/:attemptId/complete
Complete case attempt.

#### GET /clinical-cases/specialties
Get available medical specialties.

#### GET /clinical-cases/complexities
Get complexity levels.

## 🤖 AI Chat & Tutoring

#### POST /chat/message
Send message to AI tutor.

**Request Body:**
```json
{
  "message": "string",
  "context": "string",
  "sessionId": "string"
}
```

#### GET /chat/sessions
Get user's chat sessions.

#### GET /chat/sessions/:sessionId/messages
Get messages from a chat session.

#### DELETE /chat/sessions/:sessionId
Delete a chat session.

#### POST /chat/sessions/:sessionId/regenerate
Regenerate AI response.

## 🧠 AI Recommendations

#### GET /ai-recommendations/courses
Get AI-powered course recommendations.

#### GET /ai-recommendations/study-plan
Get personalized study plan.

## 📊 Analytics & Insights

#### POST /analytics/events
Track learning events.

#### GET /analytics/performance/:userId
Get user performance analytics.

#### GET /analytics/predictions/:userId
Get performance predictions.

#### GET /analytics/study-patterns/:userId
Get study pattern analysis.

#### POST /analytics/events/batch
Submit multiple analytics events.

#### GET /analytics/recommendations/:userId
Get analytics-based recommendations.

## 🔔 Notifications

#### GET /notifications
Get user notifications.

#### POST /notifications
Create a new notification (Admin only).

## 💬 Feedback

#### POST /feedback
Submit user feedback.

#### GET /feedback
Get feedback list (Admin only).

## 🛡️ Roles & Permissions

#### GET /roles
Get list of roles.

#### POST /roles
Create a new role (Admin only).

#### GET /roles/:id
Get role details.

#### PUT /roles/:id
Update role (Admin only).

#### DELETE /roles/:id
Delete role (Admin only).

#### POST /roles/initialize
Initialize default roles and permissions.

#### GET /roles/permissions
Get list of permissions.

#### POST /roles/permissions
Create a new permission (Admin only).

## ❤️ System Health

#### GET /health
Get system health status.

**Response:**
```json
{
  "status": "ok",
  "info": {
    "database": { "status": "up" },
    "redis": { "status": "up" },
    "memory_heap": { "status": "up" },
    "memory_rss": { "status": "up" },
    "storage": { "status": "up" }
  },
  "error": {},
  "details": {
    "database": { "status": "up" },
    "redis": { "status": "up" },
    "memory_heap": { "status": "up" },
    "memory_rss": { "status": "up" },
    "storage": { "status": "up" }
  }
}
```

## 🧪 Testing Endpoints

#### GET /test
Basic API test endpoint.

#### GET /test/modules
Get list of available modules.

#### GET /test/module/:name
Get specific module information.

#### GET /test/auth-check
Test authentication functionality.

#### POST /test/echo
Echo test endpoint.

## 📝 Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "data": {},
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": {}
  },
  "statusCode": 400
}
```

## 🔒 Rate Limiting

API endpoints are rate-limited to prevent abuse:
- **General endpoints**: 100 requests per 15 minutes per IP
- **Authentication endpoints**: 5 requests per 15 minutes per IP
- **File upload endpoints**: 10 requests per hour per user

## 📊 Pagination

List endpoints support pagination with the following parameters:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)
- `sort`: Sort field
- `order`: Sort order (asc/desc)

## 🏷️ Status Codes

- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `429`: Too Many Requests
- `500`: Internal Server Error
