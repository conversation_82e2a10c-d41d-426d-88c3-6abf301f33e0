# Complete Docker Environment Testing Script
# Tests all services in Docker environment including authentication

param(
    [string]$Environment = "production",
    [switch]$SkipBuild,
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

# Colors for output
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Cyan = "Cyan"

function Write-Status {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Test-ServiceHealth {
    param([string]$ServiceName, [string]$Url, [int]$MaxRetries = 30)
    
    Write-Status "🔍 Testing $ServiceName health at $Url..." $Cyan
    
    for ($i = 1; $i -le $MaxRetries; $i++) {
        try {
            $response = Invoke-RestMethod -Uri $Url -Method Get -TimeoutSec 5
            if ($response.status -eq "healthy" -or $response -eq "OK") {
                Write-Status "✅ $ServiceName is healthy!" $Green
                return $true
            }
        }
        catch {
            if ($Verbose) {
                Write-Status "   Attempt $i/$MaxRetries failed: $($_.Exception.Message)" $Yellow
            }
        }
        
        if ($i -lt $MaxRetries) {
            Start-Sleep -Seconds 2
        }
    }
    
    Write-Status "❌ $ServiceName health check failed after $MaxRetries attempts" $Red
    return $false
}

function Test-Authentication {
    param([string]$BaseUrl)
    
    Write-Status "🔐 Testing authentication flow..." $Cyan
    
    # Test user registration
    $registerData = @{
        email = "<EMAIL>"
        username = "testuser"
        name = "Test User"
        password = if ($Environment -eq "development") { "test" } else { "TestPass123!" }
        role = "student"
    }
    
    try {
        Write-Status "   Testing user registration..." $Yellow
        $registerResponse = Invoke-RestMethod -Uri "$BaseUrl/v1/auth/register" -Method Post -Body ($registerData | ConvertTo-Json) -ContentType "application/json" -TimeoutSec 10
        Write-Status "   ✅ Registration successful" $Green
        
        # Test login
        Write-Status "   Testing user login..." $Yellow
        $loginData = @{
            email = $registerData.email
            password = $registerData.password
        }
        
        $loginResponse = Invoke-RestMethod -Uri "$BaseUrl/v1/auth/login" -Method Post -Body ($loginData | ConvertTo-Json) -ContentType "application/json" -TimeoutSec 10
        
        if ($loginResponse.accessToken) {
            Write-Status "   ✅ Login successful, token received" $Green
            
            # Test protected endpoint
            Write-Status "   Testing protected endpoint..." $Yellow
            $headers = @{ Authorization = "Bearer $($loginResponse.accessToken)" }
            $profileResponse = Invoke-RestMethod -Uri "$BaseUrl/v1/auth/profile" -Method Get -Headers $headers -TimeoutSec 10
            
            if ($profileResponse.email -eq $registerData.email) {
                Write-Status "   ✅ Protected endpoint access successful" $Green
                return $true
            }
        }
    }
    catch {
        $errorMessage = $_.Exception.Message
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
            Write-Status "   ❌ Authentication test failed: HTTP $statusCode - $errorMessage" $Red
        } else {
            Write-Status "   ❌ Authentication test failed: $errorMessage" $Red
        }
    }
    
    return $false
}

function Test-ServiceCommunication {
    Write-Status "🔗 Testing service communication..." $Cyan
    
    # Test backend to analytics communication
    try {
        Write-Status "   Testing backend to analytics communication..." $Yellow
        # This would require a backend endpoint that calls analytics
        # For now, we'll test direct analytics access
        $analyticsHealth = Test-ServiceHealth "Analytics" "http://localhost:5000/health" 5
        if ($analyticsHealth) {
            Write-Status "   ✅ Analytics service accessible" $Green
        }
    }
    catch {
        Write-Status "   ❌ Service communication test failed: $($_.Exception.Message)" $Red
        return $false
    }
    
    return $true
}

# Main execution
Write-Status "=======================================================`n🐳 MedTrack Docker Environment Testing`n=======================================================" $Cyan

# Check if Docker is running
try {
    docker version | Out-Null
    Write-Status "✅ Docker is running" $Green
}
catch {
    Write-Status "❌ Docker is not running or not accessible" $Red
    exit 1
}

# Set environment file
$envFile = if ($Environment -eq "development") { ".env.docker.dev" } else { ".env.docker" }
Write-Status "🌍 Using environment: $Environment ($envFile)" $Cyan

# Copy environment file
if (Test-Path $envFile) {
    Copy-Item $envFile ".env" -Force
    Write-Status "✅ Environment file copied" $Green
} else {
    Write-Status "❌ Environment file $envFile not found" $Red
    exit 1
}

# Build and start services
if (-not $SkipBuild) {
    Write-Status "🔨 Building Docker images..." $Cyan
    docker-compose build --no-cache
    if ($LASTEXITCODE -ne 0) {
        Write-Status "❌ Docker build failed" $Red
        exit 1
    }
    Write-Status "✅ Docker images built successfully" $Green
}

Write-Status "🚀 Starting Docker services..." $Cyan
docker-compose up -d
if ($LASTEXITCODE -ne 0) {
    Write-Status "❌ Failed to start Docker services" $Red
    exit 1
}

# Wait for services to be ready
Write-Status "⏳ Waiting for services to start..." $Yellow
Start-Sleep -Seconds 10

# Test service health
$allHealthy = $true

$services = @(
    @{ Name = "PostgreSQL"; Url = "http://localhost:5432"; Skip = $true },  # PostgreSQL doesn't have HTTP health check
    @{ Name = "Redis"; Url = "http://localhost:6379"; Skip = $true },       # Redis doesn't have HTTP health check
    @{ Name = "Backend"; Url = "http://localhost:3002/health" },
    @{ Name = "Analytics"; Url = "http://localhost:5000/health" },
    @{ Name = "Frontend"; Url = "http://localhost:3000/api/health" }
)

foreach ($service in $services) {
    if (-not $service.Skip) {
        $healthy = Test-ServiceHealth $service.Name $service.Url
        $allHealthy = $allHealthy -and $healthy
    }
}

# Test authentication if backend is healthy
if ($allHealthy) {
    $authSuccess = Test-Authentication "http://localhost:3002"
    $allHealthy = $allHealthy -and $authSuccess
}

# Test service communication
if ($allHealthy) {
    $commSuccess = Test-ServiceCommunication
    $allHealthy = $allHealthy -and $commSuccess
}

# Final results
Write-Status "`n=======================================================" $Cyan
if ($allHealthy) {
    Write-Status "🎉 All Docker tests passed successfully!" $Green
    Write-Status "🌐 Frontend: http://localhost:3000" $Green
    Write-Status "🔧 Backend API: http://localhost:3002" $Green
    Write-Status "📊 Analytics: http://localhost:5000" $Green
    Write-Status "📝 API Docs: http://localhost:3002/api/docs" $Green
} else {
    Write-Status "❌ Some tests failed. Check the logs above." $Red
    Write-Status "📋 View logs with: docker-compose logs [service-name]" $Yellow
    exit 1
}
Write-Status "=======================================================" $Cyan
