# Environment Configuration Guide

This guide provides comprehensive information about configuring environment variables for MedTrack Hub across different deployment environments.

## 📋 Environment Files Overview

The application uses different environment files for different deployment stages:

- `.env.development` - Local development
- `.env.staging` - Staging environment  
- `.env.production` - Production environment
- `.env.test` - Testing environment

## 🔧 Core Environment Variables

### Application Settings
```bash
NODE_ENV=production|development|staging|test
PORT=3002
APP_NAME=MedTrack Hub
APP_VERSION=1.0.0
HOSTNAME=0.0.0.0
```

### Database Configuration
```bash
POSTGRES_HOST=your-postgres-host
POSTGRES_PORT=5432
POSTGRES_USER=medtrack_user
POSTGRES_PASSWORD=your-secure-password
POSTGRES_DB=medtrack_prod
POSTGRES_SSL=true
DATABASE_URL=postgresql://user:password@host:port/database
```

### Redis Configuration
```bash
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-secure-redis-password
REDIS_DB=0
REDIS_KEY_PREFIX=medtrack:
REDIS_TTL=3600
```

### Authentication & Security
```bash
# JWT Configuration (Generate with: openssl rand -base64 32)
JWT_SECRET=your-very-secure-jwt-secret-min-32-chars
JWT_REFRESH_SECRET=your-very-secure-refresh-secret-min-32-chars
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=medtrack-hub
JWT_AUDIENCE=medtrack-users

# Security Settings
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret
CORS_ORIGIN=https://yourdomain.com,https://www.yourdomain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100
```

### External Services
```bash
# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=medtrack-files
AWS_S3_ENDPOINT=https://s3.amazonaws.com

# Anthropic Claude API
CLAUDE_API_KEY=your-claude-api-key
CLAUDE_MODEL=claude-3-5-sonnet-20241022
CLAUDE_MAX_TOKENS=4096
CLAUDE_TEMPERATURE=0.7

# Email Service (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>
```

### URLs and Endpoints
```bash
# Frontend URLs
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
NEXT_PUBLIC_ANALYTICS_API_URL=https://analytics.yourdomain.com
FRONTEND_URL=https://yourdomain.com
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# API Configuration
API_PREFIX=v1
SWAGGER_ENABLED=true
SWAGGER_PATH=api/docs
```

### Feature Flags
```bash
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true
ENABLE_FILE_UPLOAD=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_TWO_FACTOR_AUTH=true
ENABLE_AI_CHAT=true
```

### Monitoring & Logging
```bash
LOG_LEVEL=info
LOG_FORMAT=combined
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=5

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_RETRIES=3
```

## 🏗️ Environment-Specific Configurations

### Development Environment (.env.development)
```bash
NODE_ENV=development
PORT=3002
LOG_LEVEL=debug

# Local Database
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=medtrack_dev
POSTGRES_PASSWORD=dev_password_123
POSTGRES_DB=medtrack_dev
POSTGRES_SSL=false

# Local Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=dev_redis_123

# Development JWT Secrets
JWT_SECRET=dev-jwt-secret-for-development-only-min-32-chars
JWT_REFRESH_SECRET=dev-refresh-secret-for-development-only-min-32-chars

# Local URLs
NEXT_PUBLIC_API_URL=http://localhost:3002
NEXT_PUBLIC_ANALYTICS_API_URL=http://localhost:5000
FRONTEND_URL=http://localhost:3000
CORS_ORIGIN=http://localhost:3000

# Development Features
SWAGGER_ENABLED=true
ENABLE_ANALYTICS=true
ENABLE_AI_CHAT=false  # Disable AI in dev if no API key
```

### Staging Environment (.env.staging)
```bash
NODE_ENV=staging
PORT=3002
LOG_LEVEL=info

# Staging Database
POSTGRES_HOST=staging-db.yourdomain.com
POSTGRES_PORT=5432
POSTGRES_USER=medtrack_staging
POSTGRES_PASSWORD=staging_secure_password_123
POSTGRES_DB=medtrack_staging
POSTGRES_SSL=true

# Staging Redis
REDIS_HOST=staging-redis.yourdomain.com
REDIS_PORT=6379
REDIS_PASSWORD=staging_redis_secure_password

# Staging JWT Secrets
JWT_SECRET=staging-jwt-secret-secure-min-32-chars-random
JWT_REFRESH_SECRET=staging-refresh-secret-secure-min-32-chars-random

# Staging URLs
NEXT_PUBLIC_API_URL=https://staging-api.yourdomain.com
NEXT_PUBLIC_ANALYTICS_API_URL=https://staging-analytics.yourdomain.com
FRONTEND_URL=https://staging.yourdomain.com
CORS_ORIGIN=https://staging.yourdomain.com

# Staging Features
SWAGGER_ENABLED=true
ENABLE_ANALYTICS=true
ENABLE_AI_CHAT=true
```

### Production Environment (.env.production)
```bash
NODE_ENV=production
PORT=3002
LOG_LEVEL=warn

# Production Database (Use managed service recommended)
POSTGRES_HOST=prod-db.yourdomain.com
POSTGRES_PORT=5432
POSTGRES_USER=medtrack_prod
POSTGRES_PASSWORD=super_secure_production_password_123
POSTGRES_DB=medtrack_production
POSTGRES_SSL=true

# Production Redis (Use managed service recommended)
REDIS_HOST=prod-redis.yourdomain.com
REDIS_PORT=6379
REDIS_PASSWORD=super_secure_redis_production_password

# Production JWT Secrets (Generate with: openssl rand -base64 32)
JWT_SECRET=production-jwt-secret-super-secure-random-min-32-chars
JWT_REFRESH_SECRET=production-refresh-secret-super-secure-random-min-32-chars

# Production URLs
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
NEXT_PUBLIC_ANALYTICS_API_URL=https://analytics.yourdomain.com
FRONTEND_URL=https://yourdomain.com
CORS_ORIGIN=https://yourdomain.com,https://www.yourdomain.com

# Production Security
BCRYPT_ROUNDS=12
RATE_LIMIT_MAX=50
RATE_LIMIT_WINDOW_MS=900000

# Production Features
SWAGGER_ENABLED=false  # Disable in production for security
ENABLE_ANALYTICS=true
ENABLE_AI_CHAT=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_TWO_FACTOR_AUTH=true

# Production AWS
AWS_ACCESS_KEY_ID=AKIA...
AWS_SECRET_ACCESS_KEY=...
AWS_REGION=us-east-1
AWS_S3_BUCKET=medtrack-production-files

# Production Monitoring
LOG_LEVEL=error
HEALTH_CHECK_TIMEOUT=3000
```

## 🔐 Security Best Practices

### JWT Secrets
- Use `openssl rand -base64 32` to generate secure secrets
- Use different secrets for each environment
- Rotate secrets regularly in production
- Store secrets securely (use secret management services)

### Database Security
- Use strong passwords (minimum 16 characters)
- Enable SSL/TLS connections
- Use connection pooling
- Regular backups and point-in-time recovery

### Redis Security
- Use authentication (password)
- Enable SSL/TLS if supported
- Configure appropriate timeout values
- Use separate Redis instances for different environments

### API Security
- Enable CORS with specific origins
- Use rate limiting
- Implement proper authentication
- Disable Swagger in production
- Use HTTPS in production

## 🔧 Configuration Validation

The application validates environment variables on startup using Joi schema validation. Required variables will cause startup failure if missing or invalid.

### Required Variables
- `POSTGRES_HOST`, `POSTGRES_USER`, `POSTGRES_PASSWORD`, `POSTGRES_DB`
- `JWT_SECRET`, `JWT_REFRESH_SECRET`
- `REDIS_HOST` (if Redis is enabled)

### Optional Variables
- `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY` (for file uploads)
- `CLAUDE_API_KEY` (for AI features)
- `SMTP_*` variables (for email features)

## 🚀 Quick Setup Commands

### Generate JWT Secrets
```bash
# Generate JWT secret
openssl rand -base64 32

# Generate refresh secret
openssl rand -base64 32
```

### Database Setup
```bash
# Create database user
createuser -P medtrack_user

# Create database
createdb -O medtrack_user medtrack_prod
```

### Environment File Creation
```bash
# Copy template
cp .env.example .env.production

# Edit with your values
nano .env.production
```

## 📊 Environment Variable Reference

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `NODE_ENV` | Yes | development | Application environment |
| `PORT` | No | 3002 | Server port |
| `POSTGRES_HOST` | Yes | - | Database host |
| `POSTGRES_USER` | Yes | - | Database user |
| `POSTGRES_PASSWORD` | Yes | - | Database password |
| `POSTGRES_DB` | Yes | - | Database name |
| `REDIS_HOST` | No | localhost | Redis host |
| `JWT_SECRET` | Yes | - | JWT signing secret |
| `JWT_REFRESH_SECRET` | Yes | - | JWT refresh secret |
| `CORS_ORIGIN` | No | * | Allowed CORS origins |
| `SWAGGER_ENABLED` | No | true | Enable Swagger docs |
| `LOG_LEVEL` | No | info | Logging level |
