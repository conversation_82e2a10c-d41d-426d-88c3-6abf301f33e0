{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "moduleNameMapper": {"^src/(.*)$": "<rootDir>/../src/$1"}, "testTimeout": 30000, "collectCoverageFrom": ["../src/**/*.(t|j)s", "!../src/**/*.spec.ts", "!../src/**/*.interface.ts", "!../src/**/*.dto.ts"], "coverageDirectory": "../coverage", "coverageReporters": ["text", "lcov", "html"]}