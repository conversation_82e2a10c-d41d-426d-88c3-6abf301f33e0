// src/swagger/swagger.setup.ts
import { INestApplication } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

export function setupSwagger(app: INestApplication) {
  const config = new DocumentBuilder()
    .setTitle('MedTrack Hub API')
    .setDescription(
      `
# MedTrack Hub API Documentation

A comprehensive medical education platform API providing:

## 🔐 Authentication & Authorization
- JWT-based authentication with refresh tokens
- Role-based access control (Student, Instructor, Admin, Attending Physician)
- Two-factor authentication with TOTP support
- Session management and security monitoring
- Password hashing with Argon2

## 📚 Learning Management
- Comprehensive course and unit management
- Study materials with file uploads (AWS S3)
- Learning paths with milestone tracking
- Learning goals with SMART objectives
- Progress tracking and analytics
- Interactive quizzes and assessments

## 🏥 Clinical Features
- Clinical case studies with complexity levels
- Patient simulation scenarios
- Diagnostic workflows and decision trees
- Medical knowledge base integration
- Specialty-specific content organization

## 🤖 AI-Powered Features
- AI tutoring and chat assistance (Claude integration)
- Personalized learning recommendations
- Performance prediction analytics
- Adaptive learning paths
- Smart content suggestions
- Automated study plan generation

## 📊 Analytics & Reporting
- Learning pattern analysis
- Performance metrics and insights
- Study habit tracking
- Predictive analytics for outcomes
- Real-time progress monitoring
- Comprehensive reporting dashboards

## 🔔 Communication & Collaboration
- Real-time notifications system
- User feedback and rating system
- Discussion forums and chat
- Collaborative study groups
- Peer learning features

## 🛡️ Security & Compliance
- Role-based permissions system
- Data encryption and security
- Audit logging and monitoring
- HIPAA-compliant data handling
- Rate limiting and throttling

---

**Base URL:** \`http://localhost:3002\`
**Version:** 1.0.0
**Environment:** ${process.env.NODE_ENV || 'development'}
    `,
    )
    .setVersion('1.0.0')
    .addTag('auth', '🔐 Authentication & Authorization')
    .addTag('users', '👥 User Management')
    .addTag('courses', '📚 Course Management')
    .addTag('course-categories', '📂 Course Categories')
    .addTag('materials', '📄 Study Materials')
    .addTag('units', '📖 Learning Units')
    .addTag('learning-paths', '🎯 Learning Paths')
    .addTag('learning-goals', '🎯 Learning Goals')
    .addTag('progress', '📈 Progress Tracking')
    .addTag('quiz', '🧪 Quizzes & Assessments')
    .addTag('clinical-cases', '🏥 Clinical Cases')
    .addTag('chat', '🤖 AI Chat & Tutoring')
    .addTag('ai-recommendations', '🧠 AI Recommendations')
    .addTag('analytics', '📊 Analytics & Insights')
    .addTag('notifications', '🔔 Notifications')
    .addTag('feedback', '💬 User Feedback')
    .addTag('roles', '🛡️ Roles & Permissions')
    .addTag('health', '❤️ System Health')
    .addTag('test', '🧪 Testing Endpoints')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'Enter JWT token obtained from login endpoint',
      },
      'JWT-auth',
    )
    .addSecurityRequirements('JWT-auth')
    .setContact(
      'MedTrack Hub Support',
      'https://github.com/Pkowech/medical',
      '<EMAIL>',
    )
    .setLicense('MIT', 'https://opensource.org/licenses/MIT')
    .addServer('http://localhost:3002', 'Development Server')
    .addServer('https://api.medtrackhub.com', 'Production Server')
    .setExternalDoc('GitHub Repository', 'https://github.com/Pkowech/medical')
    .build();

  const document = SwaggerModule.createDocument(app, config, {
    operationIdFactory: (controllerKey: string, methodKey: string) => methodKey,
    deepScanRoutes: true,
  });

  // Enhanced custom options with better styling
  const customOptions = {
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
      docExpansion: 'none',
      filter: true,
      showExtensions: true,
      showCommonExtensions: true,
      tryItOutEnabled: true,
      requestInterceptor: (req: any) => {
        // Add custom headers or modify requests
        req.headers['X-API-Client'] = 'Swagger-UI';
        return req;
      },
    },
    customSiteTitle: 'MedTrack Hub API Documentation',
    customfavIcon: '/favicon.ico',
    customCss: `
      .swagger-ui .topbar { display: none; }
      .swagger-ui .info { margin: 20px 0; }
      .swagger-ui .info .title { color: #2c5aa0; }
      .swagger-ui .scheme-container {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin: 20px 0;
      }
      .swagger-ui .opblock.opblock-post { border-color: #49cc90; }
      .swagger-ui .opblock.opblock-get { border-color: #61affe; }
      .swagger-ui .opblock.opblock-put { border-color: #fca130; }
      .swagger-ui .opblock.opblock-delete { border-color: #f93e3e; }
      .swagger-ui .btn.authorize {
        background-color: #2c5aa0;
        border-color: #2c5aa0;
      }
      .swagger-ui .btn.authorize:hover {
        background-color: #1e3d6f;
        border-color: #1e3d6f;
      }
    `,
    customJs: `
      window.onload = function() {
        // Add custom JavaScript for enhanced functionality
        console.log('MedTrack Hub API Documentation loaded');

        // Auto-expand authentication section
        setTimeout(() => {
          const authSection = document.querySelector('[data-tag="auth"]');
          if (authSection) {
            authSection.click();
          }
        }, 1000);
      };
    `,
  };

  SwaggerModule.setup('api/docs', app, document, customOptions);

  // Also setup a JSON endpoint for the OpenAPI spec
  SwaggerModule.setup('api/docs-json', app, document, {
    jsonDocumentUrl: 'api/docs-json',
    yamlDocumentUrl: 'api/docs-yaml',
  });
}
