import { PipeTransform, Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

interface PasswordConfig {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  allowedSpecialChars: string;
}

interface ValueWithPassword {
  password?: string;
  [key: string]: any;
}

@Injectable()
export class PasswordValidationPipe implements PipeTransform {
  constructor(private readonly configService: ConfigService) {}

  transform(value: ValueWithPassword): ValueWithPassword {
    if (!value?.password) {
      return value;
    }

    const passwordConfig = this.configService.get<PasswordConfig>('app.security.password') || {
      minLength: process.env.NODE_ENV === 'development' ? 4 : 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      allowedSpecialChars: '!@#$%^&*',
    };

    const password = value.password;
    const passwordErrors: string[] = [];

    // Length validation
    if (password.length < passwordConfig.minLength) {
      passwordErrors.push(`Password must be at least ${passwordConfig.minLength} characters long`);
    }

    // Uppercase validation
    if (passwordConfig.requireUppercase && !/[A-Z]/.test(password)) {
      passwordErrors.push('Password must contain at least one uppercase letter');
    }

    // Lowercase validation
    if (passwordConfig.requireLowercase && !/[a-z]/.test(password)) {
      passwordErrors.push('Password must contain at least one lowercase letter');
    }

    // Number validation
    if (passwordConfig.requireNumbers && !/[0-9]/.test(password)) {
      passwordErrors.push('Password must contain at least one number');
    }

    // Special character validation
    if (passwordConfig.requireSpecialChars) {
      const specialCharsRegex = new RegExp(`[${passwordConfig.allowedSpecialChars.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}]`);
      if (!specialCharsRegex.test(password)) {
        passwordErrors.push(`Password must contain at least one special character (${passwordConfig.allowedSpecialChars})`);
      }
    }

    if (passwordErrors.length > 0) {
      throw new BadRequestException({
        message: 'Password does not meet security requirements',
        details: passwordErrors,
      });
    }

    return value;
  }
}
