const http = require('http');

const testEndpoint = (path, method = 'GET', data = null) => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3002,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: body,
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
};

async function testBackend() {
  console.log('Testing MedTrack Hub Backend...\n');

  // Test endpoints
  const endpoints = [
    { path: '/v1/', name: 'Root endpoint' },
    { path: '/v1/health', name: 'Health check' },
    { path: '/v1/test', name: 'Test endpoint' },
    { path: '/v1/auth/session', name: 'Auth session' },
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`Testing ${endpoint.name} (${endpoint.path})...`);
      const result = await testEndpoint(endpoint.path);
      console.log(`  Status: ${result.statusCode}`);
      console.log(`  Response: ${result.body.substring(0, 200)}${result.body.length > 200 ? '...' : ''}`);
      console.log('');
    } catch (error) {
      console.log(`  Error: ${error.message}`);
      console.log('');
    }
  }
}

testBackend().catch(console.error);
