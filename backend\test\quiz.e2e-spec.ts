import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { DataSource } from 'typeorm';

describe('Quiz Module (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let authToken: string;
  let userId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    dataSource = moduleFixture.get<DataSource>(DataSource);
    await app.init();

    // Create a test user and get auth token
    const registerResponse = await request(app.getHttpServer())
      .post('/v1/auth/register')
      .send({
        username: 'quizuser',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        firstName: 'Quiz',
        lastName: 'User',
        role: 'student'
      });
    
    authToken = registerResponse.body.access_token;
    userId = registerResponse.body.user.id;
  });

  afterAll(async () => {
    await dataSource.destroy();
    await app.close();
  });

  describe('/quiz/unit/:unitId (GET)', () => {
    it('should get quiz questions for a unit', () => {
      const unitId = '123e4567-e89b-12d3-a456-426614174000'; // Mock unit ID
      
      return request(app.getHttpServer())
        .get(`/v1/quiz/unit/${unitId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });

    it('should fail without authentication', () => {
      const unitId = '123e4567-e89b-12d3-a456-426614174000';
      
      return request(app.getHttpServer())
        .get(`/v1/quiz/unit/${unitId}`)
        .expect(401);
    });
  });

  describe('/quiz/submit (POST)', () => {
    it('should submit quiz answers', () => {
      return request(app.getHttpServer())
        .post('/v1/quiz/submit')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          unitId: '123e4567-e89b-12d3-a456-426614174000',
          answers: [
            {
              questionId: '123e4567-e89b-12d3-a456-426614174001',
              selectedAnswer: 'A',
              timeSpent: 30
            }
          ]
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('score');
          expect(res.body).toHaveProperty('totalQuestions');
          expect(res.body).toHaveProperty('correctAnswers');
        });
    });

    it('should fail with invalid data', () => {
      return request(app.getHttpServer())
        .post('/v1/quiz/submit')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          unitId: 'invalid-id',
          answers: []
        })
        .expect(400);
    });
  });

  describe('/quiz/results/:userId/:unitId (GET)', () => {
    it('should get quiz results', () => {
      const unitId = '123e4567-e89b-12d3-a456-426614174000';
      
      return request(app.getHttpServer())
        .get(`/v1/quiz/results/${userId}/${unitId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });
  });

  describe('/quiz/rapid-review/:userId (GET)', () => {
    it('should get rapid review questions', () => {
      return request(app.getHttpServer())
        .get(`/v1/quiz/rapid-review/${userId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });
  });
});
