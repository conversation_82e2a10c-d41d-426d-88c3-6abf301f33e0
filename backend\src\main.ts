import { NestFactory } from '@nestjs/core';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { AppModule } from './app.module';
import { setupSwagger } from './swagger/swagger.setup';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { TransformInterceptor } from './common/interceptors/transform.interceptor';
import { AllExceptionsFilter } from './common/filters/all-exceptions.filter';
import { ConfigService } from '@nestjs/config';
import helmet from 'helmet';
import { rateLimit } from 'express-rate-limit';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: ['error', 'warn', 'log'],
    bufferLogs: true,
  });

  // Global pipes with validation (production-optimized)
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      enableDebugMessages: process.env.NODE_ENV !== 'production',
      validationError: {
        target: false,
        value: process.env.NODE_ENV !== 'production',
      },
      // Validation error factory
      exceptionFactory: (errors) => {
        const messages = errors.map((error) => ({
          field: error.property,
          value:
            process.env.NODE_ENV !== 'production' ? error.value : undefined,
          constraints: error.constraints,
        }));
        return {
          statusCode: 400,
          message: 'Validation failed',
          errors: messages,
        };
      },
    }),
  );

  // Global interceptors
  app.useGlobalInterceptors(
    new LoggingInterceptor(),
    new TransformInterceptor(),
  );

  // Global filters
  app.useGlobalFilters(new AllExceptionsFilter());

  // API versioning
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });

  // CORS configuration  // Enhanced CORS configuration
  app.enableCors({
    origin: process.env.ALLOWED_ORIGINS
      ? process.env.ALLOWED_ORIGINS.split(',')
      : ['http://localhost:3000'],
    methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'Accept',
      'Origin',
      'X-Requested-With',
    ],
    exposedHeaders: [
      'X-RateLimit-Limit',
      'X-RateLimit-Remaining',
      'X-RateLimit-Reset',
    ],
    credentials: true,
    maxAge: 3600, // 1 hour
  });
  // Enhanced security headers with helmet
  app.use(
    helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'", "'unsafe-inline'"],
          imgSrc: ["'self'", 'data:', 'https:'],
          connectSrc: ["'self'"],
          fontSrc: ["'self'", 'https:', 'data:'],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"],
          formAction: ["'self'"],
          workerSrc: ["'self'", 'blob:'],
          baseUri: ["'self'"],
          manifestSrc: ["'self'"],
        },
      },
      crossOriginEmbedderPolicy: true,
      crossOriginOpenerPolicy: true,
      crossOriginResourcePolicy: { policy: 'same-site' },
      dnsPrefetchControl: true,
      frameguard: { action: 'deny' },
      hidePoweredBy: true,
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
      },
      ieNoOpen: true,
      noSniff: true,
      referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
      xssFilter: true,
    }),
  );

  // Enhanced rate limiting with different limits for different endpoints
  const loginLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // limit each IP to 5 login attempts per windowMs
    message: 'Too many login attempts, please try again after 15 minutes',
    standardHeaders: true,
    legacyHeaders: false,
  });

  const apiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message:
      'Too many requests from this IP, please try again after 15 minutes',
    standardHeaders: true,
    legacyHeaders: false,
  });

  app.use('/auth/login', loginLimiter);
  app.use('/auth/register', loginLimiter);
  app.use(apiLimiter);

  const port = process.env.PORT || 3002;
  const hostname = process.env.HOSTNAME || '0.0.0.0';

  await app.listen(port, hostname);

  // Get configuration service for enhanced logging
  const configService = app.get(ConfigService);
  const environment = configService.get('NODE_ENV', 'development');
  const dbHost = configService.get('POSTGRES_HOST', 'localhost');
  const redisHost = configService.get('REDIS_HOST', 'localhost');

  // Enhanced logging with environment info
  const serverUrl = `http://${hostname === '0.0.0.0' ? 'localhost' : hostname}:${port}`;
  console.log(`=======================================================`);
  console.log(`🚀 MedTrack Hub Backend Server Started Successfully!`);
  console.log(`=======================================================`);
  console.log(`🌍 Environment: ${environment.toUpperCase()}`);
  console.log(`🔗 Server URL: ${serverUrl}`);
  console.log(`📡 Listening on: ${hostname}:${port}`);
  console.log(`🗄️  Database: ${dbHost}:${configService.get('POSTGRES_PORT', 5432)}`);
  console.log(`🔴 Redis: ${redisHost}:${configService.get('REDIS_PORT', 6379)}`);
  console.log(`=======================================================`);
  console.log(`📝 API Documentation: ${serverUrl}/api/docs`);
  console.log(`❤️  Health Check: ${serverUrl}/health`);
  console.log(`🔐 Authentication Endpoints:`);
  console.log(`   - POST ${serverUrl}/v1/auth/register`);
  console.log(`   - POST ${serverUrl}/v1/auth/login`);
  console.log(`   - GET ${serverUrl}/v1/auth/profile`);
  console.log(`   - POST ${serverUrl}/v1/auth/refresh`);
  console.log(`📊 Analytics Service: ${configService.get('ANALYTICS_SERVICE_URL', 'http://localhost:5000')}`);
  console.log(`=======================================================`);

  // Log password requirements based on environment
  const passwordConfig = configService.get('app.security.password');
  if (passwordConfig) {
    console.log(`🔒 Password Requirements (${environment}):`);
    console.log(`   - Minimum length: ${passwordConfig.minLength || (environment === 'development' ? 4 : 8)} characters`);
    console.log(`   - Uppercase required: ${passwordConfig.requireUppercase !== false}`);
    console.log(`   - Numbers required: ${passwordConfig.requireNumbers !== false}`);
    console.log(`   - Special chars required: ${passwordConfig.requireSpecialChars !== false}`);
    console.log(`=======================================================`);
  }
}
bootstrap();
