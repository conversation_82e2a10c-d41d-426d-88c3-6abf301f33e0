# Ultra-minimal backend build
FROM node:20-alpine AS builder

WORKDIR /app

# Install minimal build dependencies
RUN apk add --no-cache python3 make g++

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install pnpm and dependencies
RUN npm install -g pnpm@9.12.3 && \
    pnpm install

# Copy source and build
COPY . .
RUN pnpm run build

# Production stage - ultra-minimal
FROM node:20-alpine

WORKDIR /app

# Install only essential runtime dependencies
RUN apk add --no-cache curl && \
    adduser -D -s /bin/sh app

# Copy built application and package files
COPY --from=builder --chown=app:app /app/dist ./dist
COPY --from=builder --chown=app:app /app/package.json ./
COPY --from=builder --chown=app:app /app/pnpm-lock.yaml ./

# Install only production dependencies with pnpm
RUN npm install -g pnpm@9.12.3 && \
    pnpm install --prod && \
    pnpm store prune && \
    npm uninstall -g pnpm && \
    rm -rf ~/.npm ~/.pnpm-store /tmp/*

# Switch to non-root user
USER app

# Set environment variables
ENV NODE_ENV=production \
    PORT=3002 \
    HOSTNAME=0.0.0.0

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:${PORT}/health || exit 1

EXPOSE 3002

# Start the application
CMD ["node", "dist/main.js"]
