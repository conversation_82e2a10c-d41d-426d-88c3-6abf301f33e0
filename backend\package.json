{"name": "medtrack-hub-backend", "version": "1.0.0", "description": "", "author": "Pkowech", "private": true, "license": "UNLICENSED", "engines": {"node": ">=18.0.0"}, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:integration": "jest --config ./test/jest-integration.json", "test:all": "npm run test && npm run test:e2e && npm run test:integration", "test:e2e:watch": "jest --config ./test/jest-e2e.json --watch", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "pnpm run typeorm -- migration:generate -d src/database/datasource.ts", "migration:run": "npx typeorm-ts-node-commonjs migration:run -d src/database/datasource.ts", "migration:revert": "pnpm run typeorm -- migration:revert -d src/database/datasource.ts", "schema:sync": "pnpm run typeorm -- schema:sync -d src/database/datasource.ts", "schema:drop": "pnpm run typeorm -- schema:drop -d src/database/datasource.ts", "health-check": "ts-node scripts/health-check.ts", "setup:admin": "ts-node src/scripts/setup-admin.ts", "seed": "ts-node src/scripts/seed-database.ts", "seed:learning-paths-goals": "ts-node src/scripts/seed-database.ts", "docs:generate": "nest build && node dist/scripts/generate-docs.js", "docs:serve": "npx @redocly/cli preview-docs API_DOCUMENTATION.md", "swagger:export": "curl http://localhost:3002/api/docs-json > swagger.json", "postman:export": "swagger2postman -s swagger.json -o postman-collection.json"}, "dependencies": {"@anthropic-ai/sdk": "^0.57.0", "@aws-sdk/client-s3": "^3.782.0", "@nestjs/axios": "^4.0.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.13", "@nestjs/swagger": "^11.1.1", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "argon2": "^0.41.1", "aws-sdk": "^2.1692.0", "axios": "^1.9.0", "cache-manager": "^6.4.2", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "ioredis": "^5.6.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "multer": "1.4.5-lts.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.16.0", "qrcode": "^1.5.4", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "speakeasy": "^2.0.0", "typeorm": "^0.3.24", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.13", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/argon2": "^0.15.4", "@types/aws-sdk": "^2.7.4", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/joi": "^17.2.2", "@types/jsonwebtoken": "^9.0.9", "@types/long": "^4.0.2", "@types/multer": "^1.4.12", "@types/node": "^22.14.0", "@types/node-fetch": "^2.6.12", "@types/offscreencanvas": "^2019.7.3", "@types/passport-jwt": "^4.0.1", "@types/pg": "^8.15.4", "@types/qrcode": "^1.5.5", "@types/seedrandom": "^3.0.8", "@types/speakeasy": "^2.0.10", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@types/whatwg-url": "^13.0.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "node-gyp": "^11.2.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "sqlite3": "^5.1.7", "supertest": "^7.1.0", "ts-jest": "^29.3.1", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.2", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "pnpm": {"onlyBuiltDependencies": ["@nestjs/core", "@scarf/scarf", "aws-sdk", "core-js"], "ignoredBuiltDependencies": ["@swc/core"]}}