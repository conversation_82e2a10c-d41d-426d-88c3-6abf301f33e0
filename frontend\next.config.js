
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable standalone output for Docker deployment
  output: process.env.DOCKER_BUILD === 'true' ? 'standalone' : undefined,

  // Disable linting during build to speed up the process
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Disable TypeScript checking during build
  typescript: {
    ignoreBuildErrors: true,
  },

  // Configure experimental features for better stability
  experimental: {
    serverActions: {
      enabled: true
    }
  },
  // Webpack configuration to handle problematic packages
  webpack: (config, { isServer }) => {
    // Exclude Node.js specific packages from client bundle
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
      };

      // Ignore problematic packages
      config.externals = config.externals || [];
      config.externals.push({
        '@tensorflow/tfjs-node': 'commonjs @tensorflow/tfjs-node',
        ioredis: 'commonjs ioredis',
      });
    }

    return config;
  },

  // Add security headers
  async headers() {
    const isProd = process.env.NODE_ENV === 'production';
    return [
      {
        // Apply these headers to all routes
        source: '/:path*',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value:
              'camera=(), microphone=(), geolocation=(), interest-cohort=(), payment=(), usb=(), magnetometer=(), gyroscope=()',
          },
          {
            key: 'Content-Security-Policy',
            value: isProd
              ? "default-src 'self'; script-src 'self'; style-src 'self'; img-src 'self' data:; font-src 'self'; connect-src 'self' https://*.amazonaws.com; frame-ancestors 'none'; upgrade-insecure-requests; block-all-mixed-content"
              : "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self'; connect-src 'self' http://localhost:* https://*.amazonaws.com",
          },
        ],
      },
    ];
  },

  // API rewrites
  async rewrites() {
    return [
      // Use environment variable for API URL in production
      {
        source: '/api/:path*',
        has: [
          {
            type: 'header',
            key: 'x-skip-next-auth',
            value: '1',
          },
        ],
        destination: process.env.NEXT_PUBLIC_API_URL
          ? `${process.env.NEXT_PUBLIC_API_URL}/:path*`
          : 'http://localhost:3002/:path*',
      },
      // Add a specific rewrite for backend auth endpoints
      // Exclude NextAuth.js routes
      {
        source: '/api/auth/:path((?!callback|signin|signout|session|csrf|providers|error).*)',
        destination: process.env.NEXT_PUBLIC_API_URL
          ? `${process.env.NEXT_PUBLIC_API_URL}/auth/:path*`
          : 'http://localhost:3002/auth/:path*',
      },
    ];
  },
};

module.exports = nextConfig;
