import * as crypto from 'crypto';

export class SecurityUtils {
  /**
   * Generate a cryptographically secure random string
   */
  static generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Generate a secure hash of a string
   */
  static async hashString(str: string, salt?: string): Promise<string> {
    const finalSalt = salt || crypto.randomBytes(16).toString('hex');
    return new Promise((resolve, reject) => {
      crypto.pbkdf2(str, finalSalt, 100000, 64, 'sha512', (err, derivedKey) => {
        if (err) reject(err);
        resolve(derivedKey.toString('hex'));
      });
    });
  }

  /**
   * Validate password strength with configurable requirements
   */
  static validatePasswordStrength(
    password: string,
    config?: {
      minLength?: number;
      requireUppercase?: boolean;
      requireLowercase?: boolean;
      requireNumbers?: boolean;
      requireSpecialChars?: boolean;
      allowedSpecialChars?: string;
    }
  ): {
    isValid: boolean;
    errors: string[];
  } {
    // Default configuration (can be overridden)
    const defaultConfig = {
      minLength: process.env.NODE_ENV === 'development' ? 4 : 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      allowedSpecialChars: '!@#$%^&*',
    };

    const finalConfig = { ...defaultConfig, ...config };
    const errors: string[] = [];

    // Length validation
    if (password.length < finalConfig.minLength) {
      errors.push(`Password must be at least ${finalConfig.minLength} characters long`);
    }

    // Uppercase validation
    if (finalConfig.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    // Lowercase validation
    if (finalConfig.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    // Number validation
    if (finalConfig.requireNumbers && !/[0-9]/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    // Special character validation
    if (finalConfig.requireSpecialChars) {
      const specialCharsRegex = new RegExp(`[${finalConfig.allowedSpecialChars.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}]`);
      if (!specialCharsRegex.test(password)) {
        errors.push(`Password must contain at least one special character (${finalConfig.allowedSpecialChars})`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Sanitize user input to prevent XSS
   */
  static sanitizeInput(input: string): string {
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  /**
   * Rate limiting helper
   */
  static createRateLimiter(windowMs: number, maxRequests: number) {
    const requests = new Map<string, number[]>();

    return (ip: string): boolean => {
      const now = Date.now();
      const windowStart = now - windowMs;

      // Get existing requests for this IP
      const userRequests = requests.get(ip) || [];

      // Filter out old requests
      const recentRequests = userRequests.filter((time) => time > windowStart);

      // Check if user has exceeded limit
      if (recentRequests.length >= maxRequests) {
        return false;
      }

      // Add current request
      recentRequests.push(now);
      requests.set(ip, recentRequests);

      return true;
    };
  }
}
