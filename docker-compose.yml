services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: medtrack-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-medical_tracker}
      POSTGRES_USER: ${POSTGRES_USER:-medical}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-AU110s/6081/2021MT}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --lc-collate=C --lc-ctype=C"
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./backend/migrations:/docker-entrypoint-initdb.d
      - ./setup-database.sql:/docker-entrypoint-initdb.d/01-setup.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-medical} -d ${POSTGRES_DB:-medical_tracker}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - app-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: medtrack-redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-MedTrack2024SecureRedisPassword!} --appendonly yes
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-MedTrack2024SecureRedisPassword!}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    networks:
      - app-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: medtrack-backend
    ports:
      - "${BACKEND_PORT:-3002}:3002"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=3002
      - HOSTNAME=0.0.0.0
      # Database Configuration
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=${POSTGRES_USER:-medical}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-AU110s/6081/2021MT}
      - POSTGRES_DB=${POSTGRES_DB:-medical_tracker}
      - DATABASE_URL=postgresql://${POSTGRES_USER:-medical}:${POSTGRES_PASSWORD:-AU110s/6081/2021MT}@postgres:5432/${POSTGRES_DB:-medical_tracker}
      # Redis Configuration
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-MedTrack2024SecureRedisPassword!}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-MedTrack2024SecureRedisPassword!}@redis:6379
      # JWT Configuration
      - JWT_SECRET=${JWT_SECRET:-MedTrack2024JWTSecretKey9mN2pQ5rT8uW1xZ4cV6bN9mP2sU5vY8zA1dF4gH7jK0lM3nQ6rT9uW2xZ5cV8b}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET:-MedTrack2024RefreshJWTSecret3nQ6rT9uW2xZ5cV8bN1mP4sU7vY0zA3dF6gH9jK2lM5nQ8rT1uW4xZ7cV0bN3mP6}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-24h}
      - JWT_REFRESH_EXPIRES_IN=${JWT_REFRESH_EXPIRES_IN:-7d}
      # TypeORM Configuration
      - TYPEORM_SYNC=${TYPEORM_SYNC:-false}
      - TYPEORM_LOGGING=${TYPEORM_LOGGING:-false}
      # Password Configuration
      - PASSWORD_MIN_LENGTH=${PASSWORD_MIN_LENGTH:-8}
      - PASSWORD_REQUIRE_UPPERCASE=${PASSWORD_REQUIRE_UPPERCASE:-true}
      - PASSWORD_REQUIRE_LOWERCASE=${PASSWORD_REQUIRE_LOWERCASE:-true}
      - PASSWORD_REQUIRE_NUMBERS=${PASSWORD_REQUIRE_NUMBERS:-true}
      - PASSWORD_REQUIRE_SPECIAL=${PASSWORD_REQUIRE_SPECIAL:-true}
      # Analytics Service
      - ANALYTICS_SERVICE_URL=http://analytics:5000
      # CORS Configuration
      - CORS_ORIGIN=http://frontend:3000,http://localhost:3000
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 90s
    restart: unless-stopped
    networks:
      - app-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Python Analytics Service
  analytics:
    build:
      context: ./backend/python_analytics
      dockerfile: Dockerfile
    container_name: medtrack-analytics
    ports:
      - "${ANALYTICS_PORT:-5000}:5000"
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=5000
      # Database Configuration
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=${POSTGRES_USER:-medical}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-AU110s/6081/2021MT}
      - POSTGRES_DB=${POSTGRES_DB:-medical_tracker}
      - DATABASE_URL=postgresql://${POSTGRES_USER:-medical}:${POSTGRES_PASSWORD:-AU110s/6081/2021MT}@postgres:5432/${POSTGRES_DB:-medical_tracker}
      # JWT Configuration
      - JWT_SECRET=${JWT_SECRET:-MedTrack2024JWTSecretKey9mN2pQ5rT8uW1xZ4cV6bN9mP2sU5vY8zA1dF4gH7jK0lM3nQ6rT9uW2xZ5cV8b}
      # Frontend URL for CORS
      - FRONTEND_URL=http://frontend:3000
    depends_on:
      postgres:
        condition: service_healthy
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    networks:
      - app-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: medtrack-frontend
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=3000
      - HOSTNAME=0.0.0.0
      - DOCKER_BUILD=true
      # API URLs for internal Docker communication
      - NEXT_PUBLIC_API_URL=http://backend:3002
      - NEXT_PUBLIC_ANALYTICS_API_URL=http://analytics:5000
      # External URLs for browser access (will be overridden by nginx)
      - NEXT_PUBLIC_EXTERNAL_API_URL=http://localhost:3002
      - NEXT_PUBLIC_EXTERNAL_ANALYTICS_URL=http://localhost:5000
    depends_on:
      backend:
        condition: service_healthy
      analytics:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s
    restart: unless-stopped
    networks:
      - app-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: medtrack-nginx
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
      - analytics
    restart: unless-stopped
    networks:
      - app-network

volumes:
  postgres-data:
  redis-data:

networks:
  app-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: ************/24
