import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { DataSource } from 'typeorm';

describe('Authentication (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    dataSource = moduleFixture.get<DataSource>(DataSource);
    await app.init();
  });

  afterAll(async () => {
    await dataSource.destroy();
    await app.close();
  });

  beforeEach(async () => {
    // Clean up database before each test
    await dataSource.query('DELETE FROM users');
  });

  describe('/auth/register (POST)', () => {
    it('should register a new user successfully', () => {
      return request(app.getHttpServer())
        .post('/v1/auth/register')
        .send({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'TestPassword123!',
          firstName: 'Test',
          lastName: 'User',
          role: 'student'
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('access_token');
          expect(res.body).toHaveProperty('user');
          expect(res.body.user.email).toBe('<EMAIL>');
        });
    });

    it('should fail with invalid email', () => {
      return request(app.getHttpServer())
        .post('/v1/auth/register')
        .send({
          username: 'testuser',
          email: 'invalid-email',
          password: 'TestPassword123!',
          firstName: 'Test',
          lastName: 'User',
          role: 'student'
        })
        .expect(400);
    });

    it('should fail with weak password', () => {
      return request(app.getHttpServer())
        .post('/v1/auth/register')
        .send({
          username: 'testuser',
          email: '<EMAIL>',
          password: '123',
          firstName: 'Test',
          lastName: 'User',
          role: 'student'
        })
        .expect(400);
    });
  });

  describe('/auth/login (POST)', () => {
    beforeEach(async () => {
      // Create a test user
      await request(app.getHttpServer())
        .post('/v1/auth/register')
        .send({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'TestPassword123!',
          firstName: 'Test',
          lastName: 'User',
          role: 'student'
        });
    });

    it('should login with valid credentials', () => {
      return request(app.getHttpServer())
        .post('/v1/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'TestPassword123!'
        })
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('access_token');
          expect(res.body).toHaveProperty('user');
        });
    });

    it('should fail with invalid credentials', () => {
      return request(app.getHttpServer())
        .post('/v1/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword'
        })
        .expect(401);
    });
  });

  describe('/auth/profile (GET)', () => {
    let authToken: string;

    beforeEach(async () => {
      // Register and login to get token
      const registerResponse = await request(app.getHttpServer())
        .post('/v1/auth/register')
        .send({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'TestPassword123!',
          firstName: 'Test',
          lastName: 'User',
          role: 'student'
        });
      
      authToken = registerResponse.body.access_token;
    });

    it('should get user profile with valid token', () => {
      return request(app.getHttpServer())
        .get('/v1/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('email', '<EMAIL>');
          expect(res.body).toHaveProperty('username', 'testuser');
        });
    });

    it('should fail without token', () => {
      return request(app.getHttpServer())
        .get('/v1/auth/profile')
        .expect(401);
    });

    it('should fail with invalid token', () => {
      return request(app.getHttpServer())
        .get('/v1/auth/profile')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });
  });
});
