# Docker Startup Script for MedTrack Hub
# Starts all services in Docker with proper environment configuration

param(
    [string]$Environment = "production",
    [switch]$Build,
    [switch]$Clean,
    [switch]$Logs,
    [switch]$Test
)

$ErrorActionPreference = "Stop"

# Colors
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Cyan = "Cyan"

function Write-Status {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

Write-Status "=======================================================`n🐳 MedTrack Hub Docker Startup`n=======================================================" $Cyan

# Validate environment
if ($Environment -notin @("development", "production")) {
    Write-Status "❌ Invalid environment. Use 'development' or 'production'" $Red
    exit 1
}

# Set environment file
$envFile = if ($Environment -eq "development") { ".env.docker.dev" } else { ".env.docker" }
Write-Status "🌍 Environment: $Environment" $Cyan
Write-Status "📄 Using config: $envFile" $Cyan

# Check if environment file exists
if (-not (Test-Path $envFile)) {
    Write-Status "❌ Environment file $envFile not found!" $Red
    Write-Status "Please create the environment file first." $Yellow
    exit 1
}

# Copy environment file
Copy-Item $envFile ".env" -Force
Write-Status "✅ Environment configuration loaded" $Green

# Clean up if requested
if ($Clean) {
    Write-Status "🧹 Cleaning up existing containers and volumes..." $Yellow
    docker-compose down -v --remove-orphans
    docker system prune -f
    Write-Status "✅ Cleanup completed" $Green
}

# Build images if requested
if ($Build) {
    Write-Status "🔨 Building Docker images..." $Cyan
    docker-compose build --no-cache
    if ($LASTEXITCODE -ne 0) {
        Write-Status "❌ Docker build failed" $Red
        exit 1
    }
    Write-Status "✅ Docker images built successfully" $Green
}

# Start services
Write-Status "🚀 Starting Docker services..." $Cyan
docker-compose up -d

if ($LASTEXITCODE -ne 0) {
    Write-Status "❌ Failed to start Docker services" $Red
    exit 1
}

Write-Status "✅ Docker services started successfully" $Green

# Wait for services to be ready
Write-Status "⏳ Waiting for services to initialize..." $Yellow
Start-Sleep -Seconds 15

# Show service status
Write-Status "`n📊 Service Status:" $Cyan
docker-compose ps

# Show service URLs
Write-Status "`n🌐 Service URLs:" $Cyan
Write-Status "   Frontend:     http://localhost:3000" $Green
Write-Status "   Backend API:  http://localhost:3002" $Green
Write-Status "   Analytics:    http://localhost:5000" $Green
Write-Status "   API Docs:     http://localhost:3002/api/docs" $Green

# Show password requirements
Write-Status "`n🔒 Password Requirements ($Environment):" $Cyan
if ($Environment -eq "development") {
    Write-Status "   Minimum length: 4 characters (relaxed for testing)" $Yellow
    Write-Status "   Complexity: Not required" $Yellow
} else {
    Write-Status "   Minimum length: 8 characters" $Green
    Write-Status "   Must contain: uppercase, lowercase, number, special char" $Green
}

# Run tests if requested
if ($Test) {
    Write-Status "`n🧪 Running tests..." $Cyan
    Start-Sleep -Seconds 10  # Give services more time to fully start
    
    # Run PowerShell tests
    & "$PSScriptRoot\test-docker-complete.ps1" -Environment $Environment -SkipBuild
    
    # Run Node.js auth tests if available
    if (Get-Command node -ErrorAction SilentlyContinue) {
        Write-Status "Running authentication tests..." $Cyan
        $env:NODE_ENV = $Environment
        node "$PSScriptRoot\test-docker-auth.js"
    }
}

# Show logs if requested
if ($Logs) {
    Write-Status "`n📋 Showing service logs..." $Cyan
    docker-compose logs -f
} else {
    Write-Status "`n💡 Tips:" $Cyan
    Write-Status "   View logs: docker-compose logs [service-name]" $Yellow
    Write-Status "   Stop services: docker-compose down" $Yellow
    Write-Status "   Restart service: docker-compose restart [service-name]" $Yellow
    Write-Status "   Run tests: .\scripts\test-docker-complete.ps1" $Yellow
}

Write-Status "`n🎉 MedTrack Hub is ready!" $Green
Write-Status "=======================================================" $Cyan
