# MedTrack Hub Docker Setup Guide

This guide provides comprehensive instructions for running MedTrack Hub in Docker containers with proper authentication and service communication.

## 🐳 Docker Architecture

The MedTrack Hub application consists of the following containerized services:

- **Frontend** (Next.js) - Port 3000
- **Backend** (NestJS) - Port 3002  
- **Analytics** (Python/FastAPI) - Port 5000
- **PostgreSQL** - Port 5432
- **Redis** - Port 6379
- **Nginx** (Reverse Proxy) - Ports 80, 443, 8080

## 📋 Prerequisites

1. **Docker Desktop** installed and running
2. **Docker Compose** v2.0 or higher
3. **PowerShell** (for Windows scripts) or **Bash** (for Linux/Mac)
4. **Node.js** (for running authentication tests)

## 🚀 Quick Start

### 1. Choose Environment

**Development Environment** (Relaxed security for testing):
```powershell
.\scripts\docker-start.ps1 -Environment development -Build -Test
```

**Production Environment** (Full security):
```powershell
.\scripts\docker-start.ps1 -Environment production -Build -Test
```

### 2. Manual Setup

If you prefer manual control:

```bash
# Copy environment configuration
cp .env.docker.dev .env  # For development
# OR
cp .env.docker .env      # For production

# Build and start services
docker-compose build
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f
```

## 🔧 Environment Configuration

### Development Environment (.env.docker.dev)
- **Password Requirements**: Minimum 4 characters (relaxed)
- **Security**: Reduced for easier testing
- **Logging**: Verbose debug output
- **CORS**: Permissive settings

### Production Environment (.env.docker)
- **Password Requirements**: Minimum 8 characters + complexity
- **Security**: Full security headers and validation
- **Logging**: Production-level logging
- **CORS**: Restricted to specific origins

## 🔐 Authentication & Password Requirements

### Development Mode
```javascript
// Minimum requirements
{
  minLength: 4,
  requireUppercase: false,
  requireLowercase: false,
  requireNumbers: false,
  requireSpecialChars: false
}

// Example valid passwords: "test", "dev", "1234"
```

### Production Mode
```javascript
// Strict requirements
{
  minLength: 8,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true
}

// Example valid password: "TestPass123!"
```

## 🧪 Testing

### Automated Testing

Run comprehensive tests:
```powershell
# Test all services and authentication
.\scripts\test-docker-complete.ps1 -Environment development

# Test only authentication flow
node .\scripts\test-docker-auth.js
```

### Manual Testing

1. **Service Health Checks**:
   ```bash
   curl http://localhost:3002/health  # Backend
   curl http://localhost:5000/health  # Analytics
   curl http://localhost:3000/api/health  # Frontend
   ```

2. **User Registration** (Development):
   ```bash
   curl -X POST http://localhost:3002/v1/auth/register \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "username": "testuser",
       "name": "Test User",
       "password": "test",
       "role": "student"
     }'
   ```

3. **User Registration** (Production):
   ```bash
   curl -X POST http://localhost:3002/v1/auth/register \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "username": "testuser", 
       "name": "Test User",
       "password": "TestPass123!",
       "role": "student"
     }'
   ```

4. **User Login**:
   ```bash
   curl -X POST http://localhost:3002/v1/auth/login \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "password": "test"
     }'
   ```

5. **Protected Endpoint** (use token from login):
   ```bash
   curl -X GET http://localhost:3002/v1/auth/profile \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

## 🌐 Service URLs

When running locally:

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3002
- **Analytics Service**: http://localhost:5000
- **API Documentation**: http://localhost:3002/api/docs
- **Nginx Proxy**: http://localhost:8080

## 📊 Monitoring & Logs

### View Service Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f analytics
docker-compose logs -f postgres
docker-compose logs -f redis
```

### Service Status
```bash
# Check running containers
docker-compose ps

# Check resource usage
docker stats

# Check service health
docker-compose exec backend curl http://localhost:3002/health
```

## 🔧 Troubleshooting

### Common Issues

1. **Port Conflicts**:
   ```bash
   # Check what's using ports
   netstat -tulpn | grep :3000
   netstat -tulpn | grep :3002
   netstat -tulpn | grep :5000
   ```

2. **Database Connection Issues**:
   ```bash
   # Check PostgreSQL logs
   docker-compose logs postgres
   
   # Connect to database
   docker-compose exec postgres psql -U medical -d medical_tracker
   ```

3. **Redis Connection Issues**:
   ```bash
   # Check Redis logs
   docker-compose logs redis
   
   # Test Redis connection
   docker-compose exec redis redis-cli -a "MedTrack2024SecureRedisPassword!" ping
   ```

4. **Build Issues**:
   ```bash
   # Clean rebuild
   docker-compose down -v
   docker system prune -f
   docker-compose build --no-cache
   ```

### Service Dependencies

Services start in this order:
1. PostgreSQL & Redis (databases)
2. Backend (depends on databases)
3. Analytics (depends on databases & backend)
4. Frontend (depends on backend & analytics)
5. Nginx (depends on all services)

## 🔒 Security Features

### Implemented Security Measures

1. **Environment-based Password Validation**
2. **JWT Token Authentication**
3. **CORS Protection**
4. **Rate Limiting**
5. **Security Headers (Helmet)**
6. **Input Validation & Sanitization**
7. **Non-root Container Users**
8. **Health Check Endpoints**

### Production Security Checklist

- [ ] Strong JWT secrets configured
- [ ] Database passwords changed from defaults
- [ ] Redis password set
- [ ] CORS origins restricted
- [ ] SSL certificates configured (for HTTPS)
- [ ] Environment variables secured
- [ ] Log rotation configured
- [ ] Backup strategy implemented

## 📝 Development Workflow

1. **Start Development Environment**:
   ```bash
   .\scripts\docker-start.ps1 -Environment development -Build
   ```

2. **Make Code Changes** (hot reload enabled in development)

3. **Test Changes**:
   ```bash
   .\scripts\test-docker-complete.ps1 -Environment development
   ```

4. **View Logs**:
   ```bash
   docker-compose logs -f [service-name]
   ```

5. **Restart Service** (if needed):
   ```bash
   docker-compose restart [service-name]
   ```

## 🚀 Production Deployment

1. **Prepare Environment**:
   ```bash
   cp .env.docker .env
   # Edit .env with production values
   ```

2. **Build Production Images**:
   ```bash
   docker-compose build --no-cache
   ```

3. **Start Production Services**:
   ```bash
   docker-compose up -d
   ```

4. **Verify Deployment**:
   ```bash
   .\scripts\test-docker-complete.ps1 -Environment production
   ```

## 📁 File Structure

```
medical/
├── .env.docker              # Production environment config
├── .env.docker.dev          # Development environment config
├── docker-compose.yml       # Main Docker Compose configuration
├── scripts/
│   ├── docker-start.ps1     # Docker startup script
│   ├── test-docker-complete.ps1  # Complete Docker testing
│   └── test-docker-auth.js  # Authentication testing
├── backend/
│   ├── Dockerfile           # Backend container definition
│   └── src/main.ts         # Enhanced startup logging
├── frontend/
│   ├── Dockerfile           # Frontend container definition
│   └── next.config.js      # Docker-optimized Next.js config
├── backend/python_analytics/
│   ├── Dockerfile           # Analytics container definition
│   └── main.py             # Enhanced startup logging
└── docs/
    └── DOCKER_SETUP.md     # This documentation
```

## 🔄 Recent Improvements

### Authentication Enhancements
- ✅ Environment-aware password validation
- ✅ Configurable password requirements (dev vs prod)
- ✅ Consistent validation across frontend and backend
- ✅ JWT token integration with analytics service

### Docker Configuration
- ✅ Separate environment files for dev/prod
- ✅ Enhanced health checks with better error handling
- ✅ Improved service dependencies and startup order
- ✅ Comprehensive logging configuration
- ✅ Security-hardened container configurations

### Testing & Monitoring
- ✅ Automated Docker testing scripts
- ✅ Authentication flow testing
- ✅ Service communication validation
- ✅ Enhanced startup logging for all services
- ✅ Port display and service status reporting

### Developer Experience
- ✅ One-command Docker startup
- ✅ Environment-specific configurations
- ✅ Comprehensive documentation
- ✅ Troubleshooting guides
- ✅ Development workflow optimization

## 📞 Support

For issues or questions:
1. Check service logs: `docker-compose logs [service-name]`
2. Verify environment configuration
3. Run diagnostic tests: `.\scripts\test-docker-complete.ps1`
4. Check this documentation for troubleshooting steps
5. Review the Recent Improvements section for latest changes
