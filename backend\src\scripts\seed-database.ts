#!/usr/bin/env ts-node

import { DataSource } from 'typeorm';
import { runSeeds } from '../database/seeds';
import { AppDataSource } from '../database/datasource';

async function seedDatabase() {
  console.log('Initializing database connection...');
  
  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log('Database connection established');
    
    // Run the seeds
    await runSeeds(AppDataSource);
    
    console.log('Seeding completed successfully!');
  } catch (error) {
    console.error('Error during seeding:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('Database connection closed');
    }
  }
}

// Run the seeding if this script is executed directly
if (require.main === module) {
  seedDatabase();
}

export { seedDatabase };
