import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User, UserRole } from '../entities/user.entity';
import * as argon2 from 'argon2';

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const userRepository = app.get(getRepositoryToken(User));

  // Check if admin user exists
  const adminUser = await userRepository.findOne({
    where: { email: '<EMAIL>' },
  });

  if (!adminUser) {
    console.log('Creating admin user...');
    const hashedPassword = await argon2.hash('admin123');

    const newAdmin = new User();
    newAdmin.email = '<EMAIL>';
    newAdmin.username = 'admin';
    newAdmin.name = 'Development Admin';
    newAdmin.first_name = 'Dev';
    newAdmin.last_name = 'Admin';
    newAdmin.password_hash = hashedPassword;
    newAdmin.role = UserRole.ADMIN;
    newAdmin.is_active = true;
    newAdmin.email_verified = true;

    await userRepository.save(newAdmin);
    console.log('Admin user created successfully!');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');
  } else {
    console.log('Admin user already exists.');
  }

  await app.close();
}

bootstrap().catch(console.error);
