// Global test setup
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.POSTGRES_HOST = 'localhost';
process.env.POSTGRES_PORT = '5432';
process.env.POSTGRES_USER = 'medical';
process.env.POSTGRES_PASSWORD = 'AU110s/6081/2021MT';
process.env.POSTGRES_DB = 'medical_tracker_test';
process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';

// Increase timeout for database operations
jest.setTimeout(30000);

// Global test utilities
global.testUtils = {
  createTestUser: () => ({
    username: 'testuser',
    email: '<EMAIL>',
    password: 'TestPassword123!',
    firstName: 'Test',
    lastName: 'User',
    role: 'student'
  }),
  
  createAdminUser: () => ({
    username: 'admin',
    email: '<EMAIL>',
    password: 'AdminPassword123!',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin'
  })
};
