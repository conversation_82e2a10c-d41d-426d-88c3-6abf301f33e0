# Ultra-minimal frontend build
FROM node:20-alpine AS builder

WORKDIR /app

# Copy package files
COPY package.json ./

# Install dependencies
RUN npm install --only=production --no-audit --no-fund

# Copy source and build
COPY . .
ENV DOCKER_BUILD=true
RUN npm run build

# Production stage - ultra-minimal
FROM node:20-alpine

WORKDIR /app

# Install only essential runtime dependencies
RUN apk add --no-cache curl && \
    adduser -D -s /bin/sh app

# Set production environment variables
ENV NODE_ENV=production \
    PORT=3000 \
    HOSTNAME=0.0.0.0 \
    NEXT_TELEMETRY_DISABLED=1

# Copy built application
COPY --from=builder --chown=app:app /app/public ./public
COPY --from=builder --chown=app:app /app/.next/standalone ./
COPY --from=builder --chown=app:app /app/.next/static ./.next/static

# Switch to non-root user
USER app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:${PORT}/api/health || exit 1

EXPOSE 3000

# Start the application
CMD ["node", "server.js"]
