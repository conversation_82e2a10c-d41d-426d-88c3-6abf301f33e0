import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { DataSource } from 'typeorm';

describe('Analytics Module (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let authToken: string;
  let userId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    dataSource = moduleFixture.get<DataSource>(DataSource);
    await app.init();

    // Create a test user and get auth token
    const registerResponse = await request(app.getHttpServer())
      .post('/v1/auth/register')
      .send({
        username: 'analyticsuser',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        firstName: 'Analytics',
        lastName: 'User',
        role: 'student'
      });
    
    authToken = registerResponse.body.access_token;
    userId = registerResponse.body.user.id;
  });

  afterAll(async () => {
    await dataSource.destroy();
    await app.close();
  });

  describe('/analytics/events (POST)', () => {
    it('should track analytics events', () => {
      return request(app.getHttpServer())
        .post('/v1/analytics/events')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          eventType: 'quiz_completed',
          userId: userId,
          metadata: {
            unitId: '123e4567-e89b-12d3-a456-426614174000',
            score: 85,
            timeSpent: 300
          }
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('success', true);
        });
    });

    it('should fail without authentication', () => {
      return request(app.getHttpServer())
        .post('/v1/analytics/events')
        .send({
          eventType: 'quiz_completed',
          userId: userId,
          metadata: {}
        })
        .expect(401);
    });
  });

  describe('/analytics/performance/:userId (GET)', () => {
    it('should get user performance analytics', () => {
      return request(app.getHttpServer())
        .get(`/v1/analytics/performance/${userId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('overallScore');
          expect(res.body).toHaveProperty('completedUnits');
          expect(res.body).toHaveProperty('timeSpent');
        });
    });
  });

  describe('/analytics/predictions/:userId (GET)', () => {
    it('should get performance predictions', () => {
      return request(app.getHttpServer())
        .get(`/v1/analytics/predictions/${userId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('predictions');
          expect(Array.isArray(res.body.predictions)).toBe(true);
        });
    });
  });

  describe('/analytics/study-patterns/:userId (GET)', () => {
    it('should get study patterns', () => {
      return request(app.getHttpServer())
        .get(`/v1/analytics/study-patterns/${userId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('patterns');
          expect(Array.isArray(res.body.patterns)).toBe(true);
        });
    });
  });

  describe('/analytics/events/batch (POST)', () => {
    it('should track multiple analytics events', () => {
      return request(app.getHttpServer())
        .post('/v1/analytics/events/batch')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          events: [
            {
              eventType: 'page_view',
              userId: userId,
              metadata: { page: '/dashboard' }
            },
            {
              eventType: 'quiz_started',
              userId: userId,
              metadata: { unitId: '123e4567-e89b-12d3-a456-426614174000' }
            }
          ]
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('success', true);
          expect(res.body).toHaveProperty('processed');
        });
    });
  });

  describe('/analytics/recommendations/:userId (GET)', () => {
    it('should get personalized recommendations', () => {
      return request(app.getHttpServer())
        .get(`/v1/analytics/recommendations/${userId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('recommendations');
          expect(Array.isArray(res.body.recommendations)).toBe(true);
        });
    });
  });
});
