#!/usr/bin/env node

/**
 * Comprehensive Docker Authentication Testing Script
 * Tests authentication flow, password requirements, and JWT functionality
 */

const axios = require('axios');
const colors = require('colors');

// Configuration
const config = {
  baseUrl: process.env.API_URL || 'http://localhost:3002',
  analyticsUrl: process.env.ANALYTICS_URL || 'http://localhost:5000',
  environment: process.env.NODE_ENV || 'production',
  timeout: 10000
};

// Test data
const testUsers = {
  development: {
    email: '<EMAIL>',
    username: 'devuser',
    name: 'Dev User',
    password: 'test', // Simple password for development
    role: 'student'
  },
  production: {
    email: '<EMAIL>',
    username: 'produser',
    name: 'Prod User',
    password: 'TestPass123!', // Complex password for production
    role: 'student'
  }
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  switch (type) {
    case 'success':
      console.log(`[${timestamp}] ✅ ${message}`.green);
      break;
    case 'error':
      console.log(`[${timestamp}] ❌ ${message}`.red);
      break;
    case 'warning':
      console.log(`[${timestamp}] ⚠️  ${message}`.yellow);
      break;
    case 'info':
    default:
      console.log(`[${timestamp}] ℹ️  ${message}`.cyan);
      break;
  }
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Test functions
async function testServiceHealth(serviceName, url) {
  log(`Testing ${serviceName} health...`);
  try {
    const response = await axios.get(`${url}/health`, { timeout: config.timeout });
    if (response.status === 200) {
      log(`${serviceName} is healthy`, 'success');
      return true;
    }
  } catch (error) {
    log(`${serviceName} health check failed: ${error.message}`, 'error');
    return false;
  }
}

async function testPasswordRequirements() {
  log('Testing password requirements...');
  
  const testUser = testUsers[config.environment];
  const weakPasswords = ['123', 'test', 'password', 'abc123'];
  
  for (const weakPassword of weakPasswords) {
    try {
      const userData = { ...testUser, password: weakPassword, email: `weak${Date.now()}@test.com` };
      await axios.post(`${config.baseUrl}/v1/auth/register`, userData, { timeout: config.timeout });
      
      if (config.environment === 'production') {
        log(`Weak password "${weakPassword}" was accepted in production - this is a security issue!`, 'error');
        return false;
      } else {
        log(`Weak password "${weakPassword}" accepted in development mode`, 'warning');
      }
    } catch (error) {
      if (error.response && error.response.status === 400) {
        log(`Weak password "${weakPassword}" correctly rejected`, 'success');
      } else {
        log(`Unexpected error testing weak password: ${error.message}`, 'error');
      }
    }
  }
  
  return true;
}

async function testUserRegistration() {
  log('Testing user registration...');
  
  const testUser = { ...testUsers[config.environment] };
  testUser.email = `test${Date.now()}@medtrack.com`;
  testUser.username = `testuser${Date.now()}`;
  
  try {
    const response = await axios.post(`${config.baseUrl}/v1/auth/register`, testUser, { 
      timeout: config.timeout 
    });
    
    if (response.status === 201 && response.data.accessToken) {
      log('User registration successful', 'success');
      return { success: true, user: testUser, token: response.data.accessToken };
    } else {
      log('Registration response missing expected data', 'error');
      return { success: false };
    }
  } catch (error) {
    log(`Registration failed: ${error.response?.data?.message || error.message}`, 'error');
    return { success: false };
  }
}

async function testUserLogin(userData) {
  log('Testing user login...');
  
  try {
    const loginData = {
      email: userData.email,
      password: userData.password
    };
    
    const response = await axios.post(`${config.baseUrl}/v1/auth/login`, loginData, { 
      timeout: config.timeout 
    });
    
    if (response.status === 200 && response.data.accessToken) {
      log('User login successful', 'success');
      return { success: true, token: response.data.accessToken };
    } else {
      log('Login response missing expected data', 'error');
      return { success: false };
    }
  } catch (error) {
    log(`Login failed: ${error.response?.data?.message || error.message}`, 'error');
    return { success: false };
  }
}

async function testProtectedEndpoint(token) {
  log('Testing protected endpoint access...');
  
  try {
    const response = await axios.get(`${config.baseUrl}/v1/auth/profile`, {
      headers: { Authorization: `Bearer ${token}` },
      timeout: config.timeout
    });
    
    if (response.status === 200 && response.data.email) {
      log('Protected endpoint access successful', 'success');
      return true;
    } else {
      log('Protected endpoint response missing expected data', 'error');
      return false;
    }
  } catch (error) {
    log(`Protected endpoint access failed: ${error.response?.data?.message || error.message}`, 'error');
    return false;
  }
}

async function testAnalyticsAuthentication(token) {
  log('Testing analytics service authentication...');
  
  try {
    const response = await axios.get(`${config.analyticsUrl}/auth/status`, {
      headers: { Authorization: `Bearer ${token}` },
      timeout: config.timeout
    });
    
    if (response.status === 200 && response.data.authenticated) {
      log('Analytics authentication successful', 'success');
      return true;
    } else {
      log('Analytics authentication failed', 'error');
      return false;
    }
  } catch (error) {
    log(`Analytics authentication failed: ${error.response?.data?.detail || error.message}`, 'error');
    return false;
  }
}

async function testTokenRefresh(refreshToken) {
  log('Testing token refresh...');
  
  try {
    const response = await axios.post(`${config.baseUrl}/v1/auth/refresh`, {
      refreshToken: refreshToken
    }, { timeout: config.timeout });
    
    if (response.status === 200 && response.data.accessToken) {
      log('Token refresh successful', 'success');
      return { success: true, token: response.data.accessToken };
    } else {
      log('Token refresh response missing expected data', 'error');
      return { success: false };
    }
  } catch (error) {
    log(`Token refresh failed: ${error.response?.data?.message || error.message}`, 'error');
    return { success: false };
  }
}

// Main test execution
async function runTests() {
  console.log('======================================================='.cyan);
  console.log('🔐 MedTrack Docker Authentication Testing'.cyan);
  console.log('======================================================='.cyan);
  
  log(`Environment: ${config.environment}`);
  log(`Backend URL: ${config.baseUrl}`);
  log(`Analytics URL: ${config.analyticsUrl}`);
  
  let allTestsPassed = true;
  
  // Test service health
  const backendHealthy = await testServiceHealth('Backend', config.baseUrl);
  const analyticsHealthy = await testServiceHealth('Analytics', config.analyticsUrl);
  
  if (!backendHealthy || !analyticsHealthy) {
    log('Service health checks failed, aborting tests', 'error');
    process.exit(1);
  }
  
  // Test password requirements
  const passwordTestPassed = await testPasswordRequirements();
  allTestsPassed = allTestsPassed && passwordTestPassed;
  
  // Test user registration
  const registrationResult = await testUserRegistration();
  allTestsPassed = allTestsPassed && registrationResult.success;
  
  if (!registrationResult.success) {
    log('Registration failed, skipping remaining tests', 'error');
    process.exit(1);
  }
  
  // Test user login
  const loginResult = await testUserLogin(registrationResult.user);
  allTestsPassed = allTestsPassed && loginResult.success;
  
  if (!loginResult.success) {
    log('Login failed, skipping remaining tests', 'error');
    process.exit(1);
  }
  
  // Test protected endpoint
  const protectedEndpointPassed = await testProtectedEndpoint(loginResult.token);
  allTestsPassed = allTestsPassed && protectedEndpointPassed;
  
  // Test analytics authentication
  const analyticsAuthPassed = await testAnalyticsAuthentication(loginResult.token);
  allTestsPassed = allTestsPassed && analyticsAuthPassed;
  
  // Final results
  console.log('\n======================================================='.cyan);
  if (allTestsPassed) {
    log('🎉 All authentication tests passed!', 'success');
  } else {
    log('❌ Some authentication tests failed', 'error');
    process.exit(1);
  }
  console.log('======================================================='.cyan);
}

// Run tests
runTests().catch(error => {
  log(`Test execution failed: ${error.message}`, 'error');
  process.exit(1);
});
